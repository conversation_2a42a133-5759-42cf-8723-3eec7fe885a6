import { useState, useEffect } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { GardenPlant, Plant } from '@/types/plant';
import { DatabaseService, GardenCollection, PlantIdentification } from '@/services/database';
import { useAuth } from './useAuth';

// Helper function to map diagnosis severity to health status
const mapSeverityToHealthStatus = (severity?: string): 'healthy' | 'sick' | 'recovering' | 'critical' => {
  switch (severity?.toLowerCase()) {
    case 'mild':
    case 'moderate':
      return 'sick';
    case 'severe':
    case 'critical':
      return 'critical';
    default:
      return 'healthy';
  }
};

// Helper function to convert GardenCollection to GardenPlant
const convertToGardenPlant = (collection: GardenCollection): GardenPlant => {
  const plantData = collection.plant_identifications;
  const diagnosisData = collection.plant_diagnoses?.[0]; // Get the most recent diagnosis

  // Parse care instructions from JSON string if it's a string
  let careInstructions;
  try {
    if (typeof plantData?.care_instructions === 'string') {
      careInstructions = JSON.parse(plantData.care_instructions);
    } else {
      careInstructions = plantData?.care_instructions;
    }
  } catch (error) {
    console.error('Error parsing care instructions:', error);
    // Provide default care instructions if parsing fails
    careInstructions = {
      light: 'medium',
      water: 'medium',
      temperature: { min: 18, max: 25, unit: 'C' },
      humidity: 'medium',
      soil: 'Well-draining potting mix',
      fertilizer: 'Monthly during growing season',
      toxicity: 'none'
    };
  }

  return {
    id: collection.id,
    scientificName: plantData?.scientific_name || 'Unknown',
    commonName: plantData?.common_name || collection.nickname || 'Unknown Plant',
    imageUrl: plantData?.image_url || '',
    description: plantData?.description || '',
    careInstructions: careInstructions || {
      light: 'medium',
      water: 'medium',
      temperature: { min: 18, max: 25, unit: 'C' },
      humidity: 'medium',
      soil: 'Well-draining potting mix',
      fertilizer: 'Monthly during growing season',
      toxicity: 'none'
    },
    tags: plantData?.tags || [],
    addedDate: new Date(collection.created_at),
    createdAt: new Date(collection.created_at),
    updatedAt: new Date(collection.updated_at),
    nickname: collection.nickname,
    location: plantData?.location_taken, // Location where plant was identified
    locationInGarden: collection.location_in_garden, // Location within garden
    healthStatus: collection.health_status,
    lastWatered: collection.last_watered ? new Date(collection.last_watered) : undefined,
    lastFertilized: collection.last_fertilized ? new Date(collection.last_fertilized) : undefined,
    notes: diagnosisData?.notes || collection.notes, // Use diagnosis notes if available, otherwise garden collection notes
    isPublic: collection.is_public,
    allowCommunityTips: collection.allow_community_tips,
    plantIdentificationId: collection.plant_identification_id,
    // Add all the missing fields from plant_identifications table
    plantType: plantData?.plant_type,
    nativeRegion: plantData?.native_region,
    toxicityLevel: plantData?.toxicity_level,
    toxicityWarning: plantData?.toxicity_warning,
    growthHabit: plantData?.growth_habit,
    growthRate: plantData?.growth_rate,
    matureHeight: plantData?.mature_height,
    matureWidth: plantData?.mature_width,
    matureDescription: plantData?.mature_description,
    bloomTime: plantData?.bloom_time,
    flowerColors: plantData?.flower_colors,
    foliageType: plantData?.foliage_type,
    hardinessZones: plantData?.hardiness_zones,
    minTemperature: plantData?.min_temperature,
    pestsAndDiseases: plantData?.pests_and_diseases,
    funFacts: (() => {
      if (Array.isArray(plantData?.fun_facts)) {
        return plantData.fun_facts;
      } else if (typeof plantData?.fun_facts === 'string') {
        // If it's a string, try to split it by common delimiters
        return plantData.fun_facts.split(/[•\n]/).map(fact => fact.trim()).filter(fact => fact.length > 0);
      } else {
        return [];
      }
    })(),
    uses: plantData?.uses,
    propagation: plantData?.propagation,
    seasonalCare: plantData?.seasonal_care,
    companionPlants: plantData?.companion_plants,
    maintenanceLevel: plantData?.maintenance_level,
    diagnosis: diagnosisData ? {
      id: diagnosisData.id,
      diagnosedProblem: diagnosisData.diagnosed_problem,
      likelyCauses: diagnosisData.likely_causes,
      symptomsObserved: diagnosisData.symptoms_observed,
      severity: diagnosisData.severity,
      immediateActions: diagnosisData.immediate_actions,
      longTermCare: diagnosisData.long_term_care,
      productRecommendations: diagnosisData.product_recommendations,
      stepByStepInstructions: diagnosisData.step_by_step_instructions,
      preventionTips: diagnosisData.prevention_tips,
      prognosis: diagnosisData.prognosis,
      createdAt: new Date(diagnosisData.created_at),
    } : undefined,
  };
};

export const [GardenProvider, useGarden] = createContextHook(() => {
  const [plants, setPlants] = useState<GardenPlant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  // Load garden plants from database
  useEffect(() => {
    const loadGarden = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        // Load garden collections (plants added to garden)
        const collections = await DatabaseService.getGardenCollections(user.id);
        const gardenPlants = collections.map(convertToGardenPlant);

        // Load standalone diagnoses (diagnoses not linked to garden collections)
        const allDiagnoses = await DatabaseService.getPlantDiagnoses(user.id);
        const standaloneDiagnoses = allDiagnoses.filter(diagnosis => !diagnosis.garden_collection_id);

        // Convert standalone diagnoses to GardenPlant objects
        const standaloneDiagnosisPlants = await Promise.all(
          standaloneDiagnoses.map(async (diagnosis) => {
            // Get the plant identification data
            let plantIdentification = null;
            if (diagnosis.plant_identification_id) {
              plantIdentification = await DatabaseService.getPlantIdentification(diagnosis.plant_identification_id);
            }

            // Create a GardenPlant object from the diagnosis
            const plant: GardenPlant = {
              id: diagnosis.id, // Use diagnosis ID as the plant ID
              scientificName: plantIdentification?.scientific_name || 'Unknown',
              commonName: plantIdentification?.common_name || 'Unknown Plant',
              imageUrl: diagnosis.image_url,
              description: plantIdentification?.description || 'Plant diagnosis without identification',
              careInstructions: plantIdentification?.care_instructions ?
                (typeof plantIdentification.care_instructions === 'string' ?
                  JSON.parse(plantIdentification.care_instructions) :
                  plantIdentification.care_instructions) :
                {
                  light: 'medium' as const,
                  water: 'medium' as const,
                  temperature: { min: 18, max: 24, unit: 'C' as const },
                  humidity: 'medium' as const,
                  soil: 'Well-draining',
                  fertilizer: 'Monthly',
                  toxicity: 'none' as const,
                },
              tags: plantIdentification?.tags || [],
              confidence: plantIdentification?.confidence_score || 0,
              addedDate: new Date(diagnosis.created_at),
              createdAt: new Date(diagnosis.created_at),
              updatedAt: new Date(diagnosis.updated_at),
              healthStatus: mapSeverityToHealthStatus(diagnosis.severity),
              isPublic: diagnosis.is_public,
              notes: diagnosis.notes || '', // Use diagnosis notes
              // Add plant type and other fields from identification if available
              plantType: plantIdentification?.plant_type,
              nativeRegion: plantIdentification?.native_region,
              toxicityLevel: plantIdentification?.toxicity_level,
              toxicityWarning: plantIdentification?.toxicity_warning,
              growthHabit: plantIdentification?.growth_habit,
              growthRate: plantIdentification?.growth_rate,
              matureHeight: plantIdentification?.mature_height,
              matureWidth: plantIdentification?.mature_width,
              matureDescription: plantIdentification?.mature_description,
              bloomTime: plantIdentification?.bloom_time,
              flowerColors: plantIdentification?.flower_colors,
              foliageType: plantIdentification?.foliage_type,
              hardinessZones: plantIdentification?.hardiness_zones,
              minTemperature: plantIdentification?.min_temperature,
              pestsAndDiseases: plantIdentification?.pests_and_diseases,
              funFacts: plantIdentification?.fun_facts,
              uses: plantIdentification?.uses,
              propagation: plantIdentification?.propagation,
              seasonalCare: plantIdentification?.seasonal_care,
              companionPlants: plantIdentification?.companion_plants,
              maintenanceLevel: plantIdentification?.maintenance_level,
              diagnosis: {
                id: diagnosis.id,
                diagnosedProblem: diagnosis.diagnosed_problem,
                likelyCauses: diagnosis.likely_causes,
                symptomsObserved: diagnosis.symptoms_observed,
                severity: diagnosis.severity,
                immediateActions: diagnosis.immediate_actions,
                longTermCare: diagnosis.long_term_care,
                productRecommendations: diagnosis.product_recommendations,
                stepByStepInstructions: diagnosis.step_by_step_instructions,
                preventionTips: diagnosis.prevention_tips,
                prognosis: diagnosis.prognosis,
                createdAt: new Date(diagnosis.created_at),
              },
            };

            return plant;
          })
        );

        // Combine garden plants and standalone diagnosis plants
        const allPlants = [...gardenPlants, ...standaloneDiagnosisPlants];
        setPlants(allPlants);
      } catch (error) {
        console.error('Error loading garden:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadGarden();
  }, [user]);

  const addPlant = async (plant: Plant, nickname?: string, notes?: string, diagnosisId?: string, location?: string) => {
    if (!user) return;

    try {
      // Get diagnosis data if diagnosisId is provided to determine health status
      let healthStatus: 'healthy' | 'sick' | 'recovering' | 'critical' = 'healthy';
      if (diagnosisId) {
        try {
          const diagnosis = await DatabaseService.getPlantDiagnosis(diagnosisId);
          if (diagnosis) {
            healthStatus = mapSeverityToHealthStatus(diagnosis.severity);
          }
        } catch (error) {
          // Continue with default 'healthy' status
        }
      }

      // First create the plant identification
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: false,
        // Add all the new fields from the Plant type
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
      });

      if (!identification) {
        return;
      }

      // Then add to garden collection with appropriate health status
      const gardenItem = await DatabaseService.addToGarden({
        user_id: user.id,
        plant_identification_id: identification.id,
        nickname: nickname || plant.commonName,
        notes: notes || '',
        location_in_garden: location,
        health_status: healthStatus,
        is_public: false,
        allow_community_tips: true,
      });

      if (gardenItem) {
        // Show confirmation message
        console.log('Plant added to your garden successfully!');

        // If this plant was added from a diagnosis, link the garden collection to the diagnosis first
        if (diagnosisId) {
          try {
            await DatabaseService.updatePlantDiagnosis(diagnosisId, {
              garden_collection_id: gardenItem.id,
            });

            // Refresh the garden item from database to get the linked diagnosis data
            const updatedCollections = await DatabaseService.getGardenCollections(user.id);
            const updatedCollection = updatedCollections.find(c => c.id === gardenItem.id);

            if (updatedCollection) {
              const gardenPlant = convertToGardenPlant(updatedCollection);
              setPlants((current) => [gardenPlant, ...current]);
            } else {
              // Fallback to original item if refresh fails
              const gardenPlant = convertToGardenPlant(gardenItem);
              setPlants((current) => [gardenPlant, ...current]);
            }
          } catch (error) {
            // Still add the plant even if diagnosis linking fails
            const gardenPlant = convertToGardenPlant(gardenItem);
            setPlants((current) => [gardenPlant, ...current]);
          }
        } else {
          // No diagnosis to link, just add the plant normally
          const gardenPlant = convertToGardenPlant(gardenItem);
          setPlants((current) => [gardenPlant, ...current]);
        }
      }
    } catch (error) {
      // Error adding plant to garden
    }
  };

  const removePlant = async (plantId: string) => {
    try {
      const success = await DatabaseService.removeFromGarden(plantId);
      if (success) {
        setPlants((current) => current.filter((plant) => plant.id !== plantId));
        return true;
      } else {
        throw new Error('Failed to remove plant from database');
      }
    } catch (error) {
      throw error; // Re-throw to let the calling component handle the error
    }
  };

  const waterPlant = async (plantId: string) => {
    try {
      const updatedCollection = await DatabaseService.updateGardenCollection(plantId, {
        last_watered: new Date().toISOString(),
      });

      if (updatedCollection) {
        const updatedPlant = convertToGardenPlant(updatedCollection);
        setPlants((current) =>
          current.map((plant) =>
            plant.id === plantId ? updatedPlant : plant
          )
        );
      }
    } catch (error) {
      // Error updating plant watering
    }
  };

  const updatePlant = async (plantId: string, updates: Partial<GardenCollection>) => {
    try {
      const updatedCollection = await DatabaseService.updateGardenCollection(plantId, updates);

      if (updatedCollection) {
        const updatedPlant = convertToGardenPlant(updatedCollection);
        setPlants((current) =>
          current.map((plant) =>
            plant.id === plantId ? updatedPlant : plant
          )
        );
      }
    } catch (error) {
      // Error updating plant
    }
  };

  // Update diagnosis notes for diagnosed plants
  const updateDiagnosisNotes = async (diagnosisId: string, notes: string) => {
    try {
      const updatedDiagnosis = await DatabaseService.updateDiagnosisNotes(diagnosisId, notes);

      if (updatedDiagnosis) {
        // Update the plant in the local state
        setPlants((current) =>
          current.map((plant) => {
            if (plant.diagnosis?.id === diagnosisId) {
              return {
                ...plant,
                notes: notes, // Update the notes in the plant object
                diagnosis: {
                  ...plant.diagnosis,
                  // Keep all diagnosis data the same
                }
              };
            }
            return plant;
          })
        );
      }
    } catch (error) {
      console.error('Error updating diagnosis notes:', error);
      throw error;
    }
  };

  const shareGardenItem = async (plantId: string, isPublic: boolean, shareSettings: any) => {
    try {
      const updates: Partial<GardenCollection> = {
        is_public: isPublic,
        allow_community_tips: shareSettings.allowCommunityTips,
      };

      await updatePlant(plantId, updates);
    } catch (error) {
      throw error;
    }
  };

  const saveDiagnosisOnly = async (plant: Plant, diagnosisData?: any, isPublic: boolean = false) => {
    if (!user) return;

    try {
      // Create plant identification first
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: isPublic,
        // Add all the new fields from the Plant type
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
      });

      if (!identification) {
        throw new Error('Failed to create plant identification');
      }

      // Create diagnosis
      if (diagnosisData) {
        const normalizedSeverity = diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical';
        const savedDiagnosis = await DatabaseService.createPlantDiagnosis({
          user_id: user.id,
          plant_identification_id: identification.id,
          image_url: plant.imageUrl,
          problem_description: diagnosisData.problemDescription,
          diagnosed_problem: diagnosisData.diagnosedProblem,
          likely_causes: diagnosisData.likelyCauses,
          symptoms_observed: diagnosisData.symptomsObserved,
          severity: normalizedSeverity,
          immediate_actions: diagnosisData.immediateActions,
          long_term_care: diagnosisData.longTermCare,
          product_recommendations: diagnosisData.productRecommendations,
          step_by_step_instructions: diagnosisData.stepByStepInstructions,
          prevention_tips: diagnosisData.preventionTips,
          prognosis: diagnosisData.prognosis,
          confidence_score: diagnosisData.confidence || 0.95,
          diagnosis_source: 'openrouter_api',
          is_verified: false,
          is_public: isPublic,
        });

        if (savedDiagnosis) {
          // Create a GardenPlant object for the standalone diagnosis
          const diagnosisPlant: GardenPlant = {
            id: savedDiagnosis.id,
            scientificName: plant.scientificName,
            commonName: plant.commonName,
            imageUrl: plant.imageUrl,
            description: plant.description,
            careInstructions: plant.careInstructions,
            tags: plant.tags,
            confidence: plant.confidence,
            addedDate: new Date(savedDiagnosis.created_at),
            createdAt: new Date(savedDiagnosis.created_at),
            updatedAt: new Date(savedDiagnosis.updated_at),
            healthStatus: mapSeverityToHealthStatus(savedDiagnosis.severity),
            isPublic: isPublic,
            plantType: plant.plantType,
            nativeRegion: plant.nativeRegion,
            toxicityLevel: plant.toxicityLevel,
            toxicityWarning: plant.toxicityWarning,
            growthHabit: plant.growthHabit,
            growthRate: plant.growthRate,
            matureHeight: plant.matureHeight,
            matureWidth: plant.matureWidth,
            matureDescription: plant.matureDescription,
            bloomTime: plant.bloomTime,
            flowerColors: plant.flowerColors,
            foliageType: plant.foliageType,
            hardinessZones: plant.hardinessZones,
            minTemperature: plant.minTemperature,
            pestsAndDiseases: plant.pestsAndDiseases,
            funFacts: plant.funFacts,
            uses: plant.uses,
            propagation: plant.propagation,
            seasonalCare: plant.seasonalCare,
            companionPlants: plant.companionPlants,
            maintenanceLevel: plant.maintenanceLevel,
            diagnosis: {
              id: savedDiagnosis.id,
              diagnosedProblem: savedDiagnosis.diagnosed_problem,
              likelyCauses: savedDiagnosis.likely_causes,
              symptomsObserved: savedDiagnosis.symptoms_observed,
              severity: savedDiagnosis.severity,
              immediateActions: savedDiagnosis.immediate_actions,
              longTermCare: savedDiagnosis.long_term_care,
              productRecommendations: savedDiagnosis.product_recommendations,
              stepByStepInstructions: savedDiagnosis.step_by_step_instructions,
              preventionTips: savedDiagnosis.prevention_tips,
              prognosis: savedDiagnosis.prognosis,
              createdAt: new Date(savedDiagnosis.created_at),
            },
          };

          // Add to the plants list
          setPlants((current) => [diagnosisPlant, ...current]);
        }
      }
    } catch (error) {
      console.error('Error saving diagnosis:', error);
      throw error;
    }
  };

  const addPlantAndShare = async (plant: Plant, nickname?: string, notes?: string, diagnosisId?: string, location?: string, diagnosisData?: any) => {
    if (!user) return;

    try {
      // Get diagnosis data if diagnosisId is provided to determine health status
      let healthStatus: 'healthy' | 'sick' | 'recovering' | 'critical' = 'healthy';
      let existingDiagnosis = null;

      if (diagnosisId) {
        try {
          existingDiagnosis = await DatabaseService.getPlantDiagnosis(diagnosisId);
          if (existingDiagnosis) {
            healthStatus = mapSeverityToHealthStatus(existingDiagnosis.severity);
          }
        } catch (error) {
          // Continue with default 'healthy' status
        }
      }

      // First create the plant identification
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: true, // Make identification public for sharing
        // Add all the new fields from the Plant type
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
      });

      if (!identification) {
        return;
      }

      // Create diagnosis if we have diagnosis data but no existing diagnosis
      let savedDiagnosis = existingDiagnosis;
      if (diagnosisData && !existingDiagnosis) {
        const normalizedSeverity = diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical';
        savedDiagnosis = await DatabaseService.createPlantDiagnosis({
          user_id: user.id,
          plant_identification_id: identification.id,
          image_url: plant.imageUrl,
          problem_description: diagnosisData.problemDescription,
          diagnosed_problem: diagnosisData.diagnosedProblem,
          likely_causes: diagnosisData.likelyCauses,
          symptoms_observed: diagnosisData.symptomsObserved,
          severity: normalizedSeverity,
          immediate_actions: diagnosisData.immediateActions,
          long_term_care: diagnosisData.longTermCare,
          product_recommendations: diagnosisData.productRecommendations,
          step_by_step_instructions: diagnosisData.stepByStepInstructions,
          prevention_tips: diagnosisData.preventionTips,
          prognosis: diagnosisData.prognosis,
          confidence_score: diagnosisData.confidence || 0.95,
          diagnosis_source: 'openrouter_api',
          is_verified: false,
          is_public: true, // Make diagnosis public when sharing
        });

        if (savedDiagnosis) {
          healthStatus = mapSeverityToHealthStatus(savedDiagnosis.severity);
        }
      }

      // Then add to garden collection with sharing enabled and appropriate health status
      const gardenItem = await DatabaseService.addToGardenAndShare({
        user_id: user.id,
        plant_identification_id: identification.id,
        nickname: nickname || plant.commonName,
        notes: notes || '',
        location_in_garden: location,
        health_status: healthStatus,
        is_public: true, // Make garden item public for sharing
        allow_community_tips: true,
      });

      if (gardenItem) {
        // Show confirmation message
        console.log('Plant added to your garden and shared with the community successfully!');

        // If we have a saved diagnosis, link it to the garden collection
        if (savedDiagnosis) {
          try {
            // Update the diagnosis to link it to the garden collection
            await DatabaseService.updatePlantDiagnosis(savedDiagnosis.id, {
              garden_collection_id: gardenItem.id,
              is_public: true, // Make diagnosis public when sharing
            });

            // Add the diagnosis to the garden plant for display
            const gardenPlant = convertToGardenPlant(gardenItem);
            gardenPlant.diagnosis = {
              id: savedDiagnosis.id,
              diagnosedProblem: savedDiagnosis.diagnosed_problem || '',
              severity: savedDiagnosis.severity || 'mild',
              immediateActions: savedDiagnosis.immediate_actions || [],
              longTermCare: savedDiagnosis.long_term_care || [],
              likelyCauses: savedDiagnosis.likely_causes || [],
              symptomsObserved: savedDiagnosis.symptoms_observed || '',
              productRecommendations: savedDiagnosis.product_recommendations || [],
              stepByStepInstructions: savedDiagnosis.step_by_step_instructions || [],
              preventionTips: savedDiagnosis.prevention_tips || [],
              prognosis: savedDiagnosis.prognosis || '',
              createdAt: new Date(savedDiagnosis.created_at),
            };
            setPlants((current) => [gardenPlant, ...current]);
          } catch (error) {
            console.error('Error linking diagnosis to garden collection:', error);
            // Still add the plant to the garden even if diagnosis linking fails
            const gardenPlant = convertToGardenPlant(gardenItem);
            setPlants((current) => [gardenPlant, ...current]);
          }
        } else {
          // No diagnosis to link, just add the plant normally
          const gardenPlant = convertToGardenPlant(gardenItem);
          setPlants((current) => [gardenPlant, ...current]);
        }
      }
    } catch (error) {
      // Error adding plant to garden and sharing
    }
  };

  const shareIdentificationOnly = async (plant: Plant) => {
    if (!user) return;

    try {
      await DatabaseService.shareIdentificationOnly(plant, plant.imageUrl, user.id);
      // Show confirmation message
      console.log('Plant identification shared with the community successfully!');
    } catch (error) {
      // Error sharing identification only
    }
  };

  const shareDiagnosisOnly = async (plant: Plant, diagnosisData: any) => {
    if (!user) return;

    try {
      await DatabaseService.shareDiagnosisOnly(plant, plant.imageUrl, user.id, diagnosisData);
      // Show confirmation message
      console.log('Plant diagnosis shared with the community successfully!');
    } catch (error) {
      // Error sharing diagnosis only
    }
  };

  // New functions specifically for diagnosis page
  const addDiagnosisToGarden = async (plant: Plant, nickname?: string, notes?: string, diagnosisData?: any, location?: string) => {
    if (!user) return;

    try {
      // First create plant identification with is_public = false
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: false, // Keep private
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
      });

      if (!identification) {
        throw new Error('Failed to create plant identification');
      }

      // Create diagnosis with is_public = false
      let savedDiagnosis = null;
      if (diagnosisData) {
        const normalizedSeverity = diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical';
        savedDiagnosis = await DatabaseService.createPlantDiagnosis({
          user_id: user.id,
          plant_identification_id: identification.id,
          image_url: plant.imageUrl,
          problem_description: diagnosisData.problemDescription,
          diagnosed_problem: diagnosisData.diagnosedProblem,
          likely_causes: diagnosisData.likelyCauses,
          symptoms_observed: diagnosisData.symptomsObserved,
          severity: normalizedSeverity,
          immediate_actions: diagnosisData.immediateActions,
          long_term_care: diagnosisData.longTermCare,
          product_recommendations: diagnosisData.productRecommendations,
          step_by_step_instructions: diagnosisData.stepByStepInstructions,
          prevention_tips: diagnosisData.preventionTips,
          prognosis: diagnosisData.prognosis,
          confidence_score: diagnosisData.confidence || 0.95,
          diagnosis_source: 'openrouter_api',
          is_verified: false,
          is_public: false, // Keep private
        });
      }

      // Create garden collection with is_public = false
      const healthStatus = savedDiagnosis ? mapSeverityToHealthStatus(savedDiagnosis.severity) : 'healthy';
      const gardenItem = await DatabaseService.addToGarden({
        user_id: user.id,
        plant_identification_id: identification.id,
        nickname: nickname || plant.commonName,
        notes: notes || '',
        health_status: healthStatus,
        location_in_garden: location,
        is_public: false, // Keep private
        allow_community_tips: false,
      });

      if (gardenItem) {
        // Create the garden plant object to add to state
        const diagnosisPlant: GardenPlant = {
          id: gardenItem.id,
          nickname: gardenItem.nickname || plant.commonName,
          scientificName: plant.scientificName,
          commonName: plant.commonName,
          imageUrl: plant.imageUrl,
          description: plant.description,
          careInstructions: plant.careInstructions,
          tags: plant.tags,
          healthStatus: gardenItem.health_status,
          notes: gardenItem.notes || '',
          locationInGarden: gardenItem.location_in_garden || '',
          isPublic: gardenItem.is_public,
          allowCommunityTips: gardenItem.allow_community_tips,
          addedDate: new Date(gardenItem.created_at),
          createdAt: new Date(gardenItem.created_at),
          updatedAt: new Date(gardenItem.updated_at),
          plantIdentificationId: identification.id,
          diagnosis: savedDiagnosis ? {
            id: savedDiagnosis.id,
            diagnosedProblem: savedDiagnosis.diagnosed_problem || '',
            severity: savedDiagnosis.severity || 'mild',
            immediateActions: savedDiagnosis.immediate_actions || [],
            longTermCare: savedDiagnosis.long_term_care || [],
            likelyCauses: savedDiagnosis.likely_causes || [],
            symptomsObserved: savedDiagnosis.symptoms_observed || '',
            productRecommendations: savedDiagnosis.product_recommendations || [],
            stepByStepInstructions: savedDiagnosis.step_by_step_instructions || [],
            preventionTips: savedDiagnosis.prevention_tips || [],
            prognosis: savedDiagnosis.prognosis || '',
            createdAt: new Date(savedDiagnosis.created_at),
          } : undefined,
        };

        // Add to the plants list
        setPlants((current) => [diagnosisPlant, ...current]);
      }
    } catch (error) {
      console.error('Error saving diagnosis to garden:', error);
      throw error;
    }
  };

  const addDiagnosisAndShare = async (plant: Plant, nickname?: string, notes?: string, diagnosisData?: any, location?: string) => {
    if (!user) return;

    try {
      // First create plant identification with is_public = true
      const identification = await DatabaseService.createPlantIdentification({
        user_id: user.id,
        image_url: plant.imageUrl,
        scientific_name: plant.scientificName,
        common_name: plant.commonName,
        description: plant.description,
        care_instructions: JSON.stringify(plant.careInstructions),
        tags: plant.tags,
        identification_source: 'ai',
        is_verified: false,
        is_public: true, // Make public for sharing
        plant_type: plant.plantType,
        native_region: plant.nativeRegion,
        toxicity_level: plant.toxicityLevel,
        toxicity_warning: plant.toxicityWarning,
        growth_habit: plant.growthHabit,
        growth_rate: plant.growthRate,
        mature_height: plant.matureHeight,
        mature_width: plant.matureWidth,
        mature_description: plant.matureDescription,
        bloom_time: plant.bloomTime,
        flower_colors: plant.flowerColors,
        foliage_type: plant.foliageType,
        hardiness_zones: plant.hardinessZones,
        min_temperature: plant.minTemperature,
        pests_and_diseases: plant.pestsAndDiseases,
        fun_facts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonal_care: plant.seasonalCare,
        companion_plants: plant.companionPlants,
        maintenance_level: plant.maintenanceLevel,
      });

      if (!identification) {
        throw new Error('Failed to create plant identification');
      }

      // Create diagnosis with is_public = true
      let savedDiagnosis = null;
      if (diagnosisData) {
        const normalizedSeverity = diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical';
        savedDiagnosis = await DatabaseService.createPlantDiagnosis({
          user_id: user.id,
          plant_identification_id: identification.id,
          image_url: plant.imageUrl,
          problem_description: diagnosisData.problemDescription,
          diagnosed_problem: diagnosisData.diagnosedProblem,
          likely_causes: diagnosisData.likelyCauses,
          symptoms_observed: diagnosisData.symptomsObserved,
          severity: normalizedSeverity,
          immediate_actions: diagnosisData.immediateActions,
          long_term_care: diagnosisData.longTermCare,
          product_recommendations: diagnosisData.productRecommendations,
          step_by_step_instructions: diagnosisData.stepByStepInstructions,
          prevention_tips: diagnosisData.preventionTips,
          prognosis: diagnosisData.prognosis,
          confidence_score: diagnosisData.confidence || 0.95,
          diagnosis_source: 'openrouter_api',
          is_verified: false,
          is_public: true, // Make public for sharing
        });
      }

      // Create garden collection with is_public = true
      const healthStatus = savedDiagnosis ? mapSeverityToHealthStatus(savedDiagnosis.severity) : 'healthy';
      const gardenItem = await DatabaseService.addToGardenAndShare({
        user_id: user.id,
        plant_identification_id: identification.id,
        nickname: nickname || plant.commonName,
        notes: notes || '',
        health_status: healthStatus,
        location_in_garden: location,
        is_public: true, // Make public for sharing
        allow_community_tips: true,
      });

      if (gardenItem) {
        // Create the garden plant object to add to state
        const diagnosisPlant: GardenPlant = {
          id: gardenItem.id,
          nickname: gardenItem.nickname || plant.commonName,
          scientificName: plant.scientificName,
          commonName: plant.commonName,
          imageUrl: plant.imageUrl,
          description: plant.description,
          careInstructions: plant.careInstructions,
          tags: plant.tags,
          healthStatus: gardenItem.health_status,
          notes: gardenItem.notes || '',
          locationInGarden: gardenItem.location_in_garden || '',
          isPublic: gardenItem.is_public,
          allowCommunityTips: gardenItem.allow_community_tips,
          addedDate: new Date(gardenItem.created_at),
          createdAt: new Date(gardenItem.created_at),
          updatedAt: new Date(gardenItem.updated_at),
          plantIdentificationId: identification.id,
          diagnosis: savedDiagnosis ? {
            id: savedDiagnosis.id,
            diagnosedProblem: savedDiagnosis.diagnosed_problem || '',
            severity: savedDiagnosis.severity || 'mild',
            immediateActions: savedDiagnosis.immediate_actions || [],
            longTermCare: savedDiagnosis.long_term_care || [],
            likelyCauses: savedDiagnosis.likely_causes || [],
            symptomsObserved: savedDiagnosis.symptoms_observed || '',
            productRecommendations: savedDiagnosis.product_recommendations || [],
            stepByStepInstructions: savedDiagnosis.step_by_step_instructions || [],
            preventionTips: savedDiagnosis.prevention_tips || [],
            prognosis: savedDiagnosis.prognosis || '',
            createdAt: new Date(savedDiagnosis.created_at),
          } : undefined,
        };

        // Add to the plants list
        setPlants((current) => [diagnosisPlant, ...current]);
      }
    } catch (error) {
      console.error('Error saving and sharing diagnosis:', error);
      throw error;
    }
  };

  // New function to save diagnosis only without creating plant identification
  const saveDiagnosisOnlyToTable = async (plant: Plant, notes?: string, diagnosisData?: any) => {
    if (!user) return;

    try {
      // Create diagnosis directly without plant identification
      const savedDiagnosis = await DatabaseService.createDiagnosisOnly(
        plant,
        plant.imageUrl,
        user.id,
        diagnosisData,
        notes
      );

      if (!savedDiagnosis) {
        throw new Error('Failed to save diagnosis');
      }

      // Create a minimal garden plant object for the diagnosed tab
      const diagnosisPlant: GardenPlant = {
        id: savedDiagnosis.id, // Use diagnosis ID as the plant ID for diagnosed-only plants
        plantIdentificationId: undefined, // No plant identification for diagnosis-only
        nickname: plant.commonName,
        notes: savedDiagnosis.notes || notes || '',
        healthStatus: mapSeverityToHealthStatus(savedDiagnosis.severity || 'mild'),
        locationInGarden: '',
        dateAcquired: savedDiagnosis.created_at,
        lastWatered: undefined,
        lastFertilized: undefined,
        lastRepotted: undefined,
        wateringFrequencyDays: undefined,
        fertilizingFrequencyDays: undefined,
        isPublic: false,
        allowCommunityTips: false,
        createdAt: new Date(savedDiagnosis.created_at),
        updatedAt: new Date(savedDiagnosis.updated_at),
        // Plant data from the diagnosis
        scientificName: plant.scientificName,
        commonName: plant.commonName,
        description: plant.description,
        careInstructions: plant.careInstructions,
        tags: plant.tags,
        imageUrl: plant.imageUrl,
        plantType: plant.plantType,
        nativeRegion: plant.nativeRegion,
        toxicityLevel: plant.toxicityLevel,
        toxicityWarning: plant.toxicityWarning,
        growthHabit: plant.growthHabit,
        growthRate: plant.growthRate,
        matureHeight: plant.matureHeight,
        matureWidth: plant.matureWidth,
        matureDescription: plant.matureDescription,
        bloomTime: plant.bloomTime,
        flowerColors: plant.flowerColors,
        foliageType: plant.foliageType,
        hardinessZones: plant.hardinessZones,
        minTemperature: plant.minTemperature,
        pestsAndDiseases: plant.pestsAndDiseases,
        funFacts: plant.funFacts,
        uses: plant.uses,
        propagation: plant.propagation,
        seasonalCare: plant.seasonalCare,
        companionPlants: plant.companionPlants,
        maintenanceLevel: plant.maintenanceLevel,
        // Diagnosis data
        diagnosis: {
          id: savedDiagnosis.id,
          diagnosedProblem: savedDiagnosis.diagnosed_problem || '',
          severity: savedDiagnosis.severity || 'mild',
          immediateActions: savedDiagnosis.immediate_actions || [],
          longTermCare: savedDiagnosis.long_term_care || [],
          likelyCauses: savedDiagnosis.likely_causes || [],
          symptomsObserved: savedDiagnosis.symptoms_observed || '',
          productRecommendations: savedDiagnosis.product_recommendations || [],
          stepByStepInstructions: savedDiagnosis.step_by_step_instructions || [],
          preventionTips: savedDiagnosis.prevention_tips || [],
          prognosis: savedDiagnosis.prognosis || '',
          createdAt: new Date(savedDiagnosis.created_at),
        },
      };

      // Add to the plants list
      setPlants((current) => [diagnosisPlant, ...current]);
    } catch (error) {
      console.error('Error saving diagnosis only:', error);
      throw error;
    }
  };

  return {
    plants,
    isLoading,
    addPlant,
    addPlantAndShare,
    saveDiagnosisOnly,
    shareIdentificationOnly,
    shareDiagnosisOnly,
    removePlant,
    waterPlant,
    updatePlant,
    updateDiagnosisNotes,
    shareGardenItem,
    mapSeverityToHealthStatus,
    // New diagnosis-specific functions
    addDiagnosisToGarden,
    addDiagnosisAndShare,
    saveDiagnosisOnlyToTable,
  };
});
